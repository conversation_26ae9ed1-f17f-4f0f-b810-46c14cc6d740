import { useRef, useMemo, Suspense, useEffect, useState } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { 
  Sphere, 
  MeshDistortMaterial, 
  Float, 
  OrbitControls,
  Stars,
  Environment,
  Sparkles as Sparkles3D
} from '@react-three/drei';
import * as THREE from 'three';

// Ultra-optimized animated sphere with minimal complexity
const OptimizedSphere = () => {
  const meshRef = useRef<THREE.Mesh>(null);
  const { viewport } = useThree();
  
  useFrame((state) => {
    if (meshRef.current) {
      // Simplified rotation for better performance
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.2;
    }
  });

  const scale = viewport.width > 6 ? 1.8 : 1.2;

  return (
    <Float
      speed={1}
      rotationIntensity={0.1}
      floatIntensity={0.2}
    >
      <Sphere
        ref={meshRef}
        args={[1, 32, 32]} // Reduced polygon count
        scale={scale}
      >
        <MeshDistortMaterial
          color="#4F46E5"
          attach="material"
          distort={0.2} // Reduced distortion
          speed={1}
          roughness={0.1}
          metalness={0.9}
          emissive="#4F46E5"
          emissiveIntensity={0.1}
          clearcoat={1}
          clearcoatRoughness={0.1}
        />
      </Sphere>
    </Float>
  );
};

// Ultra-lightweight neural network
const LightweightNeuralNetwork = () => {
  const points = useRef<THREE.Points>(null);
  
  const { positions, colors } = useMemo(() => {
    const nodeCount = 50; // Drastically reduced
    const positions = new Float32Array(nodeCount * 3);
    const colors = new Float32Array(nodeCount * 3);
    
    for (let i = 0; i < nodeCount; i++) {
      const radius = 2 + Math.random() * 2;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.acos(2 * Math.random() - 1);
      
      positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
      positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      positions[i * 3 + 2] = radius * Math.cos(phi);
      
      // Simple color variation
      colors[i * 3] = 0.3 + Math.random() * 0.4; // R
      colors[i * 3 + 1] = 0.6 + Math.random() * 0.4; // G
      colors[i * 3 + 2] = 0.8 + Math.random() * 0.2; // B
    }
    
    return { positions, colors };
  }, []);

  useFrame((state) => {
    if (points.current) {
      points.current.rotation.y += 0.001;
    }
  });

  return (
    <points ref={points}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={positions.length / 3}
          array={positions}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-color"
          count={colors.length / 3}
          array={colors}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.03}
        transparent
        opacity={0.8}
        sizeAttenuation
        vertexColors
      />
    </points>
  );
};

// Minimal floating elements
const MinimalFloatingElements = () => {
  const elements = useMemo(() => {
    return Array.from({ length: 4 }, (_, i) => ({ // Reduced to 4
      position: [
        (Math.random() - 0.5) * 8,
        (Math.random() - 0.5) * 8,
        (Math.random() - 0.5) * 8
      ] as [number, number, number],
      rotation: [Math.random() * Math.PI, Math.random() * Math.PI, Math.random() * Math.PI] as [number, number, number],
      scale: 0.05 + Math.random() * 0.1,
      color: ['#4F46E5', '#00D4AA', '#8B5CF6'][Math.floor(Math.random() * 3)]
    }));
  }, []);

  return (
    <group>
      {elements.map((element, index) => (
        <Float
          key={index}
          speed={0.5 + Math.random() * 1}
          rotationIntensity={0.2}
          floatIntensity={0.3}
        >
          <mesh
            position={element.position}
            rotation={element.rotation}
            scale={element.scale}
          >
            <sphereGeometry args={[0.5, 6, 6]} /> {/* Minimal geometry */}
            <meshBasicMaterial
              color={element.color}
              transparent
              opacity={0.6}
              wireframe
            />
          </mesh>
        </Float>
      ))}
    </group>
  );
};

const AIBrain = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 50);
    
    // Only render when in viewport for better performance
    const observer = new IntersectionObserver(
      ([entry]) => {
        setShouldRender(entry.isIntersecting);
      },
      { threshold: 0.1, rootMargin: '100px' }
    );
    
    const container = document.querySelector('#ai-brain-container');
    if (container) {
      observer.observe(container);
    }
    
    return () => {
      clearTimeout(timer);
      observer.disconnect();
    };
  }, []);

  return (
    <div id="ai-brain-container" className="w-full h-full relative scroll-optimized">
      {shouldRender && (
        <Canvas
          camera={{ position: [0, 0, 6], fov: 60 }}
          style={{ background: 'transparent' }}
          gl={{ 
            antialias: false, // Disable antialiasing for performance
            alpha: true,
            powerPreference: "high-performance",
            stencil: false,
            depth: true,
            logarithmicDepthBuffer: false,
            precision: "lowp" // Use low precision for better performance
          }}
          dpr={[1, 1.2]} // Reduced DPR
          performance={{ min: 0.3 }} // Lower performance threshold
          frameloop="demand" // Only render when needed
        >
          <Suspense fallback={null}>
            {/* Minimal lighting setup */}
            <ambientLight intensity={0.6} />
            <directionalLight 
              position={[5, 5, 5]} 
              intensity={0.8}
              castShadow={false}
            />
            
            {/* Simplified environment */}
            <Environment preset="night" />
            
            {/* Minimal star field */}
            <Stars 
              radius={50} 
              depth={30} 
              count={1500} // Drastically reduced
              factor={2} 
              saturation={0} 
              fade 
              speed={0.2}
            />
            
            {/* Main components */}
            <LightweightNeuralNetwork />
            <OptimizedSphere />
            <MinimalFloatingElements />
            
            {/* Minimal sparkles */}
            <Sparkles3D
              count={30} // Very few particles
              scale={2}
              size={1.5}
              speed={0.2}
              opacity={0.6}
              color="#00D4AA"
              noise={0.3}
            />
            
            {/* Optimized camera movement */}
            <OrbitControls 
              enableZoom={false}
              enablePan={false}
              autoRotate
              autoRotateSpeed={0.2}
              maxPolarAngle={Math.PI / 2}
              minPolarAngle={Math.PI / 2}
              dampingFactor={0.1}
              enableDamping
            />
          </Suspense>
        </Canvas>
      )}
      
      {/* Minimal gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-background/30 via-transparent to-background/30 pointer-events-none" />
      
      {/* Loading state */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
        </div>
      )}
    </div>
  );
};

export default AIBrain;