import { motion, useScroll, useTransform, useSpring, useInView } from 'framer-motion';
import { useRef, useState, useEffect } from 'react';
import { 
  Brain, 
  Code, 
  Database, 
  Zap, 
  Globe, 
  Settings, 
  Sparkles, 
  ArrowRight, 
  Play,
  Cpu,
  Bot,
  Layers,
  Smartphone,
  Cloud,
  Shield,
  TrendingUp,
  Users,
  Target,
  Rocket
} from 'lucide-react';
import AIBrain from './AIBrain';

const Services = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [hoveredService, setHoveredService] = useState<number | null>(null);
  const [activeService, setActiveService] = useState(0);
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });
  
  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);
  const scale = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.8, 1, 1, 0.8]);
  
  const springY = useSpring(y, { stiffness: 100, damping: 30 });
  const springOpacity = useSpring(opacity, { stiffness: 100, damping: 30 });
  const springScale = useSpring(scale, { stiffness: 100, damping: 30 });

  const services = [
    {
      id: 0,
      icon: Brain,
      title: 'AI Agents & Automation',
      subtitle: 'Intelligent Solutions',
      description: 'Build powerful AI agents that automate complex tasks, enhance customer experiences, and drive business growth with cutting-edge machine learning.',
      features: [
        { name: 'Custom AI Agents', icon: Bot, description: 'Intelligent chatbots and virtual assistants' },
        { name: 'Process Automation', icon: Cpu, description: 'Streamline workflows with AI-powered automation' },
        { name: 'Predictive Analytics', icon: TrendingUp, description: 'Data-driven insights and forecasting' },
        { name: 'Natural Language Processing', icon: Brain, description: 'Advanced text and speech understanding' }
      ],
      color: 'primary',
      gradient: 'from-blue-500/20 via-purple-500/20 to-cyan-500/20',
      bgGradient: 'from-blue-500/5 via-purple-500/5 to-cyan-500/5',
      accent: '#3B82F6',
      stats: { projects: '12+', satisfaction: '98%', efficiency: '300%' }
    },
    {
      id: 1,
      icon: Code,
      title: 'Modern Web Development',
      subtitle: 'Cutting-Edge Solutions',
      description: 'Create stunning, high-performance web applications with the latest technologies, ensuring scalability and exceptional user experiences.',
      features: [
        { name: 'Responsive Design', icon: Smartphone, description: 'Perfect across all devices and screen sizes' },
        { name: 'Progressive Web Apps', icon: Globe, description: 'Native app-like experiences on the web' },
        { name: 'Performance Optimization', icon: Zap, description: 'Lightning-fast loading and smooth interactions' },
        { name: 'Modern Frameworks', icon: Layers, description: 'React, Next.js, and cutting-edge tech stack' }
      ],
      color: 'secondary',
      gradient: 'from-emerald-500/20 via-teal-500/20 to-green-500/20',
      bgGradient: 'from-emerald-500/5 via-teal-500/5 to-green-500/5',
      accent: '#10B981',
      stats: { projects: '25+', satisfaction: '100%', efficiency: '250%' }
    },
    {
      id: 2,
      icon: Database,
      title: 'Custom Integrations',
      subtitle: 'Seamless Connections',
      description: 'Connect your systems with powerful APIs, cloud solutions, and custom integrations that work perfectly with your existing infrastructure.',
      features: [
        { name: 'API Development', icon: Cloud, description: 'Robust and scalable API solutions' },
        { name: 'Third-party Integrations', icon: Settings, description: 'Connect with your favorite tools and services' },
        { name: 'Database Design', icon: Database, description: 'Optimized data architecture and management' },
        { name: 'Security & Compliance', icon: Shield, description: 'Enterprise-grade security and data protection' }
      ],
      color: 'accent',
      gradient: 'from-orange-500/20 via-red-500/20 to-pink-500/20',
      bgGradient: 'from-orange-500/5 via-red-500/5 to-pink-500/5',
      accent: '#F59E0B',
      stats: { projects: '18+', satisfaction: '96%', efficiency: '280%' }
    }
  ];

  // Auto-rotate through services
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveService((prev) => (prev + 1) % services.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [services.length]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <section ref={containerRef} id="services" className="py-32 relative overflow-hidden min-h-screen">
      {/* Advanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-background via-background/95 to-background" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/10 via-transparent to-transparent" />
      
      {/* Floating geometric elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            variants={floatingVariants}
            animate="animate"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`
            }}
            className="absolute w-2 h-2 bg-primary/30 rounded-full blur-sm"
          />
        ))}
      </div>

      {/* 3D Brain Background */}
      <div className="absolute inset-0 opacity-20">
        <AIBrain />
      </div>

      <motion.div 
        style={{ y: springY, opacity: springOpacity, scale: springScale }}
        className="container mx-auto px-6 relative z-10"
      >
        {/* Enhanced Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-3 glass-card px-8 py-4 rounded-full mb-8 group cursor-pointer hover:bg-white/10 transition-all duration-300"
          >
            <Sparkles className="h-5 w-5 text-primary animate-pulse" />
            <span className="font-semibold text-sm tracking-wide">Our Expertise</span>
            <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
          </motion.div>
          
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-5xl md:text-7xl font-bold mb-8 leading-tight"
          >
            Transformative{' '}
            <span className="gradient-text bg-gradient-to-r from-primary via-purple-500 to-cyan-500 bg-clip-text text-transparent">
              Solutions
            </span>
          </motion.h2>
          
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed"
          >
            We specialize in creating intelligent, scalable solutions that push the boundaries 
            of what's possible in the digital realm. From AI agents to modern web applications.
          </motion.p>
        </motion.div>

        {/* Interactive Service Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid lg:grid-cols-3 gap-8 mb-20"
        >
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              variants={cardVariants}
              whileHover={{ 
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
              onHoverStart={() => setHoveredService(index)}
              onHoverEnd={() => setHoveredService(null)}
              className="group relative cursor-pointer"
            >
              <div className={`relative h-full rounded-3xl p-8 transition-all duration-500 ${
                hoveredService === index 
                  ? 'bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20' 
                  : 'bg-gradient-to-br from-white/5 to-white/2 backdrop-blur-sm border border-white/10'
              }`}>
                
                {/* Animated background gradient */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
                
                {/* Glow effect */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${service.bgGradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-xl`} />
                
                {/* Icon with animation */}
                <motion.div
                  whileHover={{ rotate: 360, scale: 1.1 }}
                  transition={{ duration: 0.6 }}
                  className={`relative inline-flex p-4 rounded-2xl mb-6 ${
                    service.color === 'primary' ? 'bg-blue-500/20 text-blue-400' :
                    service.color === 'secondary' ? 'bg-emerald-500/20 text-emerald-400' :
                    'bg-orange-500/20 text-orange-400'
                  }`}
                >
                  <service.icon className="h-8 w-8" />
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </motion.div>

                {/* Content */}
                <div className="relative">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.2 }}
                    className="text-sm font-medium text-primary mb-2"
                  >
                    {service.subtitle}
                  </motion.div>
                  
                  <h3 className="text-2xl font-bold mb-4 group-hover:text-white transition-colors duration-300">
                    {service.title}
                  </h3>
                  
                  <p className="text-muted-foreground mb-8 leading-relaxed group-hover:text-white/80 transition-colors duration-300">
                    {service.description}
                  </p>

                  {/* Interactive Features */}
                  <div className="space-y-4 mb-8">
                    {service.features.map((feature, featureIndex) => (
                      <motion.div
                        key={feature.name}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ delay: 0.3 + featureIndex * 0.1 }}
                        className="flex items-start gap-3 group/feature"
                      >
                        <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                          service.color === 'primary' ? 'bg-blue-400' :
                          service.color === 'secondary' ? 'bg-emerald-400' :
                          'bg-orange-400'
                        }`} />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <feature.icon className="h-4 w-4 text-muted-foreground group-hover/feature:text-primary transition-colors" />
                            <span className="font-medium text-sm">{feature.name}</span>
                          </div>
                          <p className="text-xs text-muted-foreground group-hover/feature:text-white/70 transition-colors">
                            {feature.description}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    {Object.entries(service.stats).map(([key, value]) => (
                      <div key={key} className="text-center">
                        <div className="text-lg font-bold text-primary">{value}</div>
                        <div className="text-xs text-muted-foreground capitalize">{key}</div>
                      </div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-white transition-colors"
                  >
                    <span>Learn More</span>
                    <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </motion.div>
                </div>

                {/* Hover border effect */}
                <div className={`absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${
                  service.color === 'primary' ? 'ring-2 ring-blue-500/50' :
                  service.color === 'secondary' ? 'ring-2 ring-emerald-500/50' :
                  'ring-2 ring-orange-500/50'
                }`} />
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="text-center"
        >
          <div className="glass-card p-12 rounded-3xl relative overflow-hidden">
            {/* Background effects */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-purple-500/10 to-cyan-500/10" />
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent" />
            
            <div className="relative">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-primary/20 text-primary mb-6"
              >
                <Rocket className="h-5 w-5" />
                <span className="font-medium">Ready to Start?</span>
              </motion.div>
              
              <h3 className="text-3xl md:text-4xl font-bold mb-4">
                Let's Build Something{' '}
                <span className="gradient-text bg-gradient-to-r from-primary to-purple-500 bg-clip-text text-transparent">
                  Amazing
                </span>
              </h3>
              
              <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
                Transform your ideas into reality with our cutting-edge web development and AI solutions. 
                Get started today and experience the future of digital innovation.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center gap-3 px-8 py-4 bg-primary text-white rounded-2xl font-medium hover:bg-primary/90 transition-colors group"
                >
                  <Play className="h-5 w-5 group-hover:scale-110 transition-transform" />
                  Start Your Project
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center gap-3 px-8 py-4 glass-card rounded-2xl font-medium hover:bg-white/10 transition-colors group"
                >
                  <Users className="h-5 w-5 group-hover:scale-110 transition-transform" />
                  Schedule a Call
                </motion.button>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Services;