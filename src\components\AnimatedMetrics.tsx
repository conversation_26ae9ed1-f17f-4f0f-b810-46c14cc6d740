import { useEffect, useState } from 'react';
import { motion, useScroll, useTransform, useMotionValueEvent } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import CountUp from 'react-countup';
import { 
  Rocket, 
  Zap, 
  TrendingUp, 
  Users, 
  Code2, 
  Brain, 
  Target, 
  Clock,
  Star,
  ArrowUpRight,
  Sparkles
} from 'lucide-react';

const AnimatedMetrics = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.2
  });

  const [hoveredMetric, setHoveredMetric] = useState<number | null>(null);
  const [isCounting, setIsCounting] = useState(false);
  const { scrollYProgress } = useScroll();
  
  // Parallax effect for background elements
  const backgroundY = useTransform(scrollYProgress, [0, 1], ['0%', '50%']);
  const backgroundScale = useTransform(scrollYProgress, [0, 1], [1, 1.2]);

  // Startup-appropriate metrics
  const metrics = [
    {
      icon: Rocket,
      value: 12,
      suffix: '+',
      label: 'Innovation Projects',
      description: 'Cutting-edge solutions in development',
      color: 'primary',
      delay: 0.1,
      trend: '+150%',
      trendDirection: 'up'
    },
    {
      icon: Brain,
      value: 8,
      suffix: '',
      label: 'AI Models Trained',
      description: 'Machine learning algorithms optimized',
      color: 'secondary',
      delay: 0.2,
      trend: '+200%',
      trendDirection: 'up'
    },
    {
      icon: TrendingUp,
      value: 95,
      suffix: '%',
      label: 'Success Rate',
      description: 'Project completion and client satisfaction',
      color: 'accent',
      delay: 0.3,
      trend: '+12%',
      trendDirection: 'up'
    },
    {
      icon: Clock,
      value: 24,
      suffix: 'h',
      label: 'Response Time',
      description: 'Average time to first response',
      color: 'primary',
      delay: 0.4,
      trend: '-40%',
      trendDirection: 'down'
    }
  ];

  // Additional startup stats
  const startupStats = [
    {
      icon: Users,
      value: 3,
      label: 'Team Members',
      description: 'Expert developers & designers'
    },
    {
      icon: Code2,
      value: 15,
      label: 'Technologies',
      description: 'Modern stack & frameworks'
    },
    {
      icon: Target,
      value: 100,
      suffix: '%',
      label: 'Focus',
      description: 'Dedicated to your success'
    },
    {
      icon: Star,
      value: 5,
      label: 'Quality',
      description: 'Premium service delivery'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      y: 50,
      scale: 0.8,
      rotateX: -15
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      rotateX: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  useEffect(() => {
    if (inView) {
      setIsCounting(true);
    }
  }, [inView]);

  return (
    <section className="py-32 relative overflow-hidden">
      {/* Advanced Background Effects */}
      <motion.div 
        className="absolute inset-0 neural-grid opacity-10"
        style={{ y: backgroundY }}
      />
      
      {/* Floating Orbs */}
      <motion.div
        className="absolute top-20 left-10 w-20 h-20 bg-primary/20 rounded-full blur-xl"
        variants={floatingVariants}
        animate="animate"
      />
      <motion.div
        className="absolute top-40 right-20 w-16 h-16 bg-secondary/20 rounded-full blur-xl"
        variants={floatingVariants}
        animate="animate"
        style={{ animationDelay: '1s' }}
      />
      <motion.div
        className="absolute bottom-20 left-1/4 w-24 h-24 bg-accent/20 rounded-full blur-xl"
        variants={floatingVariants}
        animate="animate"
        style={{ animationDelay: '2s' }}
      />

      {/* Gradient Background */}
      <motion.div 
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-radial rounded-full blur-3xl opacity-30"
        style={{ scale: backgroundScale }}
      />
      
      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 glass-card px-6 py-3 rounded-full mb-6"
          >
            <Sparkles className="h-5 w-5 text-primary animate-pulse" />
            <span className="font-medium">Our Journey</span>
          </motion.div>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            Building the{' '}
            <span className="gradient-text">Future</span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            As a startup, we're driven by innovation and rapid growth. Every metric tells a story 
            of our commitment to excellence and our vision for the future.
          </p>
        </motion.div>

        {/* Main Metrics Grid */}
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-20"
        >
          {metrics.map((metric, index) => (
            <motion.div
              key={metric.label}
              variants={itemVariants}
              onHoverStart={() => setHoveredMetric(index)}
              onHoverEnd={() => setHoveredMetric(null)}
              whileHover={{ 
                scale: 1.05,
                y: -10,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
              className="group relative cursor-pointer"
            >
              <div className="glass-ultra p-8 text-center relative overflow-hidden h-full">
                {/* Animated Background */}
                <motion.div
                  className={`absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 ${
                    metric.color === 'primary' ? 'bg-gradient-to-br from-primary/20 via-primary/10 to-transparent' :
                    metric.color === 'secondary' ? 'bg-gradient-to-br from-secondary/20 via-secondary/10 to-transparent' :
                    'bg-gradient-to-br from-accent/20 via-accent/10 to-transparent'
                  }`}
                  animate={hoveredMetric === index ? { scale: [1, 1.1, 1] } : { scale: 1 }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
                
                {/* Icon with Advanced Animation */}
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={inView ? { scale: 1, rotate: 0 } : { scale: 0, rotate: -180 }}
                  transition={{ delay: metric.delay + 0.2, type: "spring", stiffness: 200 }}
                  whileHover={{ rotate: 360, scale: 1.1 }}
                  className={`inline-flex p-4 rounded-2xl mb-6 relative ${
                    metric.color === 'primary' ? 'bg-primary/10 text-primary' :
                    metric.color === 'secondary' ? 'bg-secondary/10 text-secondary' :
                    'bg-accent/10 text-accent'
                  }`}
                >
                  <metric.icon className="h-8 w-8" />
                  
                  {/* Icon Glow Effect */}
                  <motion.div
                    className={`absolute inset-0 rounded-2xl ${
                      metric.color === 'primary' ? 'bg-primary/20' :
                      metric.color === 'secondary' ? 'bg-secondary/20' :
                      'bg-accent/20'
                    } blur-xl`}
                    animate={hoveredMetric === index ? { scale: [1, 1.5, 1] } : { scale: 1 }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                </motion.div>

                {/* Counter with Trend */}
                <div className="mb-4">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={inView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.5 }}
                    transition={{ delay: metric.delay + 0.4 }}
                    className="flex items-center justify-center gap-2 mb-2"
                  >
                    <span className={`text-4xl lg:text-5xl font-bold ${
                      metric.color === 'primary' ? 'text-primary' :
                      metric.color === 'secondary' ? 'text-secondary' :
                      'text-accent'
                    }`}>
                      {isCounting && (
                        <CountUp
                          start={0}
                          end={metric.value}
                          duration={2.5}
                          delay={metric.delay}
                          preserveValue
                        />
                      )}
                      {metric.suffix}
                    </span>
                    
                    {/* Trend Indicator */}
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: -10 }}
                      transition={{ delay: metric.delay + 0.8 }}
                      className={`flex items-center gap-1 text-sm font-semibold ${
                        metric.trendDirection === 'up' ? 'text-green-400' : 'text-red-400'
                      }`}
                    >
                      <ArrowUpRight className={`h-4 w-4 ${
                        metric.trendDirection === 'down' ? 'rotate-90' : ''
                      }`} />
                      {metric.trend}
                    </motion.div>
                  </motion.div>
                </div>

                {/* Label and Description */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
                  transition={{ delay: metric.delay + 0.6 }}
                >
                  <h3 className="text-lg font-semibold mb-2 text-foreground">
                    {metric.label}
                  </h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {metric.description}
                  </p>
                </motion.div>

                {/* Hover Effects */}
                <motion.div
                  className="absolute inset-0 border-2 border-transparent rounded-2xl"
                  animate={hoveredMetric === index ? {
                    borderColor: metric.color === 'primary' ? 'hsl(var(--primary))' :
                                metric.color === 'secondary' ? 'hsl(var(--secondary))' :
                                'hsl(var(--accent))',
                    opacity: [0, 0.3, 0]
                  } : { opacity: 0 }}
                  transition={{ duration: 1, repeat: Infinity }}
                />
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Startup Stats Row */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {startupStats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
              whileHover={{ scale: 1.05 }}
              className="glass-card p-6 text-center group hover:bg-white/5 transition-all duration-300"
            >
              <div className={`inline-flex p-3 rounded-xl mb-4 ${
                index % 4 === 0 ? 'bg-primary/10 text-primary' :
                index % 4 === 1 ? 'bg-secondary/10 text-secondary' :
                index % 4 === 2 ? 'bg-accent/10 text-accent' :
                'bg-primary/10 text-primary'
              }`}>
                <stat.icon className="h-6 w-6" />
              </div>
              <div className="text-2xl font-bold mb-2">
                {stat.value}{stat.suffix || ''}
              </div>
              <div className="text-sm font-medium text-foreground mb-1">
                {stat.label}
              </div>
              <div className="text-xs text-muted-foreground">
                {stat.description}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="text-center"
        >
          <div className="glass-ultra p-8 rounded-3xl max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 1 }}
              className="inline-flex items-center gap-2 glass-card px-6 py-3 rounded-full mb-6"
            >
              <Rocket className="h-5 w-5 text-primary animate-pulse" />
              <span className="font-medium">Ready to Scale?</span>
            </motion.div>
            
            <h3 className="text-3xl font-bold mb-4">
              Join Us in Building the{' '}
              <span className="gradient-text">Next Big Thing</span>
            </h3>
            
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              We're not just a startup – we're a movement. Every project we take on is an opportunity 
              to push boundaries, innovate, and create something extraordinary.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
              >
                Start Your Project
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-4 glass-card border border-white/20 text-foreground font-semibold rounded-xl hover:bg-white/5 transition-all duration-300"
              >
                Learn More
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AnimatedMetrics;