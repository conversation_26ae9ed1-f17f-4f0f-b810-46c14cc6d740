import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Search, Palette, Code, Rocket, CheckCircle } from 'lucide-react';

const ProcessTimeline = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const processes = [
    {
      icon: Search,
      title: 'Discover',
      description: 'We dive deep into understanding your business goals, target audience, and project requirements.',
      details: ['Market Research', 'User Analysis', 'Competitive Study', 'Requirements Gathering'],
      color: 'primary',
      delay: 0.1
    },
    {
      icon: Palette,
      title: 'Design',
      description: 'Creating stunning, user-centric designs that perfectly represent your brand and engage users.',
      details: ['UI/UX Design', 'Brand Integration', 'Prototyping', 'User Testing'],
      color: 'secondary',
      delay: 0.2
    },
    {
      icon: Code,
      title: 'Develop',
      description: 'Building robust, scalable solutions with cutting-edge technologies and AI integration.',
      details: ['Full-Stack Development', 'AI Integration', 'Performance Optimization', 'Quality Assurance'],
      color: 'accent',
      delay: 0.3
    },
    {
      icon: Rocket,
      title: 'Deploy',
      description: 'Launching your project with comprehensive testing, optimization, and ongoing support.',
      details: ['Launch Strategy', 'Performance Testing', 'SEO Optimization', '24/7 Support'],
      color: 'primary',
      delay: 0.4
    }
  ];

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 circuit-pattern opacity-10" />
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[1000px] h-[400px] bg-secondary/5 rounded-full blur-3xl" />
      
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 glass-card px-6 py-3 rounded-full mb-6"
          >
            <CheckCircle className="h-5 w-5 text-primary animate-pulse" />
            <span className="font-medium">Our Process</span>
          </motion.div>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            How We Build{' '}
            <span className="gradient-text">Excellence</span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Our proven 4-step methodology ensures every project is delivered with 
            precision, creativity, and cutting-edge technology.
          </p>
        </motion.div>

        {/* Timeline */}
        <motion.div
          ref={ref}
          initial={{ opacity: 0 }}
          animate={inView ? { opacity: 1 } : { opacity: 0 }}
          className="relative"
        >
          {/* Central line */}
          <motion.div
            initial={{ scaleY: 0 }}
            animate={inView ? { scaleY: 1 } : { scaleY: 0 }}
            transition={{ duration: 1.5, delay: 0.5 }}
            className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-primary via-secondary to-accent rounded-full origin-top hidden lg:block"
            style={{ height: '80%', top: '10%' }}
          />

          <div className="grid lg:grid-cols-2 gap-16 lg:gap-8">
            {processes.map((process, index) => (
              <motion.div
                key={process.title}
                initial={{ 
                  opacity: 0, 
                  x: index % 2 === 0 ? -50 : 50,
                  y: 30
                }}
                animate={inView ? { 
                  opacity: 1, 
                  x: 0, 
                  y: 0 
                } : { 
                  opacity: 0, 
                  x: index % 2 === 0 ? -50 : 50,
                  y: 30
                }}
                transition={{ 
                  duration: 0.8, 
                  delay: process.delay,
                  type: "spring",
                  stiffness: 100
                }}
                className={`relative ${index % 2 === 0 ? 'lg:pr-8' : 'lg:pl-8 lg:col-start-2'}`}
              >
                {/* Timeline dot */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={inView ? { scale: 1 } : { scale: 0 }}
                  transition={{ delay: process.delay + 0.3, type: "spring", stiffness: 200 }}
                  className={`absolute top-8 ${
                    index % 2 === 0 ? 'lg:right-0' : 'lg:left-0'
                  } lg:transform lg:-translate-x-1/2 lg:translate-x-0 w-6 h-6 rounded-full hidden lg:block ${
                    process.color === 'primary' ? 'bg-primary shadow-glow-primary' :
                    process.color === 'secondary' ? 'bg-secondary shadow-glow-secondary' :
                    'bg-accent shadow-glow-accent'
                  }`}
                />

                {/* Content card */}
                <div className="glass-ultra p-8 group hover:scale-105 transition-all duration-300">
                  <div className="flex items-start gap-6">
                    {/* Icon */}
                    <motion.div
                      initial={{ rotate: -180, scale: 0 }}
                      animate={inView ? { rotate: 0, scale: 1 } : { rotate: -180, scale: 0 }}
                      transition={{ delay: process.delay + 0.2, type: "spring", stiffness: 200 }}
                      className={`flex-shrink-0 p-4 rounded-2xl ${
                        process.color === 'primary' ? 'bg-primary/10 text-primary' :
                        process.color === 'secondary' ? 'bg-secondary/10 text-secondary' :
                        'bg-accent/10 text-accent'
                      }`}
                    >
                      <process.icon className="h-8 w-8" />
                    </motion.div>

                    <div className="flex-1">
                      {/* Step number */}
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={inView ? { opacity: 1 } : { opacity: 0 }}
                        transition={{ delay: process.delay + 0.1 }}
                        className="text-sm font-mono text-muted-foreground mb-2"
                      >
                        Step {(index + 1).toString().padStart(2, '0')}
                      </motion.div>

                      {/* Title */}
                      <motion.h3
                        initial={{ opacity: 0, y: 10 }}
                        animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
                        transition={{ delay: process.delay + 0.3 }}
                        className="text-2xl font-bold mb-4 group-hover:text-primary transition-colors"
                      >
                        {process.title}
                      </motion.h3>

                      {/* Description */}
                      <motion.p
                        initial={{ opacity: 0, y: 10 }}
                        animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
                        transition={{ delay: process.delay + 0.4 }}
                        className="text-muted-foreground mb-6 leading-relaxed"
                      >
                        {process.description}
                      </motion.p>

                      {/* Details */}
                      <motion.ul
                        initial={{ opacity: 0 }}
                        animate={inView ? { opacity: 1 } : { opacity: 0 }}
                        transition={{ delay: process.delay + 0.5 }}
                        className="space-y-2"
                      >
                        {process.details.map((detail, detailIndex) => (
                          <motion.li
                            key={detail}
                            initial={{ opacity: 0, x: -10 }}
                            animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: -10 }}
                            transition={{ delay: process.delay + 0.6 + detailIndex * 0.1 }}
                            className="flex items-center gap-3 text-sm"
                          >
                            <div className={`w-2 h-2 rounded-full ${
                              process.color === 'primary' ? 'bg-primary' :
                              process.color === 'secondary' ? 'bg-secondary' :
                              'bg-accent'
                            }`} />
                            <span>{detail}</span>
                          </motion.li>
                        ))}
                      </motion.ul>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="text-center mt-20"
        >
          <p className="text-muted-foreground mb-6 text-lg">
            Ready to start your journey with NovaWebb?
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="neuro-button px-8 py-4 rounded-2xl font-semibold text-primary cursor-pointer"
          >
            Start Your Project
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default ProcessTimeline;