import { useEffect, useRef, useCallback, useState } from 'react';

interface UltraSmoothScrollOptions {
  throttleMs?: number;
  debounceMs?: number;
  enableParallax?: boolean;
  enableSmoothScroll?: boolean;
  enablePerformanceMonitoring?: boolean;
}

interface ScrollState {
  x: number;
  y: number;
  direction: 'up' | 'down' | null;
  velocity: number;
  isScrolling: boolean;
}

interface SmoothScrollOptions {
  offset?: number;
  behavior?: ScrollBehavior;
}

export const useUltraSmoothScroll = (options: UltraSmoothScrollOptions = {}) => {
  const {
    throttleMs = 8, // 120fps for ultra-smooth scrolling
    debounceMs = 50,
    enableParallax = true,
    enableSmoothScroll = true,
    enablePerformanceMonitoring = false
  } = options;

  const [scrollState, setScrollState] = useState<ScrollState>({
    x: 0,
    y: 0,
    direction: null,
    velocity: 0,
    isScrolling: false
  });

  const lastScrollTime = useRef(0);
  const lastScrollY = useRef(0);
  const scrollTimeout = useRef<NodeJS.Timeout>();
  const rafId = useRef<number>();
  const performanceData = useRef({ fps: 60, frameCount: 0, lastTime: 0 });

  // Ultra-optimized scroll handler with RAF
  const handleScroll = useCallback(() => {
    const now = performance.now();
    
    // Throttle to maintain smooth performance
    if (now - lastScrollTime.current >= throttleMs) {
      const currentScrollY = window.scrollY;
      const currentScrollX = window.scrollX;
      
      // Calculate direction and velocity
      const direction = currentScrollY > lastScrollY.current ? 'down' : 'up';
      const velocity = Math.abs(currentScrollY - lastScrollY.current) / throttleMs;
      
      setScrollState({
        x: currentScrollX,
        y: currentScrollY,
        direction,
        velocity,
        isScrolling: true
      });
      
      lastScrollY.current = currentScrollY;
      lastScrollTime.current = now;
      
      // Performance monitoring
      if (enablePerformanceMonitoring) {
        performanceData.current.frameCount++;
        if (now - performanceData.current.lastTime >= 1000) {
          const fps = Math.round((performanceData.current.frameCount * 1000) / (now - performanceData.current.lastTime));
          performanceData.current.fps = Math.min(fps, 120);
          performanceData.current.frameCount = 0;
          performanceData.current.lastTime = now;
        }
      }
      
      // Debounced scroll end detection
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
      
      scrollTimeout.current = setTimeout(() => {
        setScrollState(prev => ({ ...prev, isScrolling: false, direction: null }));
      }, debounceMs);
    }
  }, [throttleMs, debounceMs, enablePerformanceMonitoring]);

  // Ultra-optimized parallax effect
  const applyParallax = useCallback((elements: NodeListOf<Element>, speed: number = 0.3) => {
    if (!enableParallax) return;
    
    const scrollY = window.scrollY;
    
    elements.forEach((element) => {
      if (element instanceof HTMLElement) {
        const yPos = -(scrollY * speed);
        // Use transform3d for hardware acceleration
        element.style.transform = `translate3d(0, ${yPos}px, 0)`;
      }
    });
  }, [enableParallax]);

  // Ultra-smooth scroll to element
  const smoothScrollTo = useCallback((target: string | Element, options: SmoothScrollOptions = {}) => {
    if (!enableSmoothScroll) return;
    
    const element = typeof target === 'string' ? document.querySelector(target) : target;
    if (!element) return;
    
    const elementRect = element.getBoundingClientRect();
    const offsetTop = elementRect.top + window.scrollY - (options.offset || 0);
    
    // Use native smooth scrolling for better performance
    window.scrollTo({
      top: offsetTop,
      behavior: options.behavior || 'smooth'
    });
  }, [enableSmoothScroll]);

  // Performance-optimized scroll event listener
  useEffect(() => {
    const throttledScroll = () => {
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
      
      rafId.current = requestAnimationFrame(handleScroll);
    };

    // Use passive listener for better performance
    window.addEventListener('scroll', throttledScroll, { passive: true });
    
    // Apply initial scroll position
    setScrollState({
      x: window.scrollX,
      y: window.scrollY,
      direction: null,
      velocity: 0,
      isScrolling: false
    });

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [handleScroll]);

  // Auto-apply parallax to elements with data-parallax attribute
  useEffect(() => {
    if (!enableParallax) return;
    
    const parallaxElements = document.querySelectorAll('[data-parallax]');
    
    const applyParallaxToElements = () => {
      parallaxElements.forEach((element) => {
        const speed = parseFloat(element.getAttribute('data-parallax') || '0.3');
        const scrollY = window.scrollY;
        const yPos = -(scrollY * speed);
        
        if (element instanceof HTMLElement) {
          element.style.transform = `translate3d(0, ${yPos}px, 0)`;
        }
      });
    };
    
    const throttledParallax = () => {
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
      rafId.current = requestAnimationFrame(applyParallaxToElements);
    };
    
    window.addEventListener('scroll', throttledParallax, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', throttledParallax);
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, [enableParallax]);

  // Performance utilities
  const getPerformanceData = () => ({
    fps: performanceData.current.fps,
    scrollVelocity: scrollState.velocity,
    isScrolling: scrollState.isScrolling
  });

  return {
    scrollState,
    applyParallax,
    smoothScrollTo,
    getPerformanceData
  };
};

// Ultra-optimized scroll utilities
export const ultraScrollUtils = {
  // Ultra-fast throttle function
  throttle: <T extends (...args: unknown[]) => unknown>(
    func: T,
    limit: number
  ): T => {
    let inThrottle: boolean;
    return ((...args: unknown[]) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }) as T;
  },

  // Ultra-fast debounce function
  debounce: <T extends (...args: unknown[]) => unknown>(
    func: T,
    delay: number
  ): T => {
    let timeoutId: NodeJS.Timeout;
    return ((...args: unknown[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    }) as T;
  },

  // Get scroll percentage with caching
  getScrollPercentage: (() => {
    let cachedPercentage = 0;
    let lastScrollY = 0;
    
    return (): number => {
      const scrollY = window.scrollY;
      if (scrollY !== lastScrollY) {
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        cachedPercentage = (scrollY / docHeight) * 100;
        lastScrollY = scrollY;
      }
      return cachedPercentage;
    };
  })(),

  // Check if element is in viewport with caching
  isInViewport: (() => {
    const cache = new WeakMap<Element, { result: boolean; timestamp: number }>();
    const CACHE_DURATION = 100; // Cache for 100ms
    
    return (element: Element): boolean => {
      const now = performance.now();
      const cached = cache.get(element);
      
      if (cached && now - cached.timestamp < CACHE_DURATION) {
        return cached.result;
      }
      
      const rect = element.getBoundingClientRect();
      const result = (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
      );
      
      cache.set(element, { result, timestamp: now });
      return result;
    };
  })(),

  // Ultra-smooth scroll to top
  scrollToTop: (behavior: ScrollBehavior = 'smooth') => {
    window.scrollTo({
      top: 0,
      behavior
    });
  },

  // Ultra-smooth scroll to bottom
  scrollToBottom: (behavior: ScrollBehavior = 'smooth') => {
    window.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior
    });
  },

  // Enable hardware acceleration for element
  enableHardwareAcceleration: (element: HTMLElement) => {
    element.style.transform = 'translate3d(0, 0, 0)';
    element.style.willChange = 'transform';
    element.style.backfaceVisibility = 'hidden';
  },

  // Disable hardware acceleration for element
  disableHardwareAcceleration: (element: HTMLElement) => {
    element.style.transform = '';
    element.style.willChange = '';
    element.style.backfaceVisibility = '';
  }
};

export default useUltraSmoothScroll;
