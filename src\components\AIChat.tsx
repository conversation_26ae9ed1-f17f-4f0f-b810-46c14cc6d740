import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Bot, User, X, MessageCircle, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
}

const AIChat = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'bot',
      content: "Hi! I'm <PERSON><PERSON><PERSON>, your AI assistant. I can help you learn about our services, pricing, or answer any questions about AI solutions and web development. How can I help you today?",
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const quickResponses = [
    "Tell me about your services",
    "What's your pricing?",
    "How do you use AI?",
    "Can you show me examples?"
  ];

  const botResponses: { [key: string]: string } = {
    'services': "We offer three main services:\n\n🤖 **AI Solutions** - Intelligent chatbots, automation, and analytics\n💻 **Web Development** - Modern, scalable applications with Next.js and React\n🔗 **Custom Integrations** - APIs, cloud solutions, and data pipelines\n\nWhich service interests you most?",
    'pricing': "Our pricing is project-based and depends on complexity:\n\n📱 **Basic Web App**: ₹50,000 - ₹2,00,000\n🤖 **AI Integration**: ₹1,00,000 - ₹5,00,000\n🏢 **Enterprise Solutions**: ₹3,00,000+\n\nWe offer free consultations to provide accurate quotes. Would you like to schedule one?",
    'ai': "We leverage cutting-edge AI technologies:\n\n🧠 **Machine Learning** - Predictive analytics and recommendations\n💬 **Natural Language Processing** - Intelligent chatbots and content analysis\n👁️ **Computer Vision** - Image recognition and processing\n📊 **Deep Learning** - Neural networks for complex problem-solving\n\nWhat specific AI application interests you?",
    'examples': "Here are some recent projects:\n\n🛒 **E-commerce AI** - 45% conversion increase with smart recommendations\n📈 **Analytics Dashboard** - Real-time insights with 99.9% uptime\n💰 **Trading Bot** - 23% average returns using neural networks\n\nWould you like to see more details about any of these?",
    'default': "I'd be happy to help! You can ask me about:\n\n• Our AI solutions and web development services\n• Pricing and project timelines\n• Technical capabilities and technologies\n• Case studies and examples\n• Getting started with your project\n\nWhat would you like to know?"
  };

  const generateBotResponse = (userMessage: string): string => {
    const message = userMessage.toLowerCase();
    
    if (message.includes('service') || message.includes('what do you do')) {
      return botResponses.services;
    } else if (message.includes('price') || message.includes('cost') || message.includes('pricing')) {
      return botResponses.pricing;
    } else if (message.includes('ai') || message.includes('artificial intelligence') || message.includes('machine learning')) {
      return botResponses.ai;
    } else if (message.includes('example') || message.includes('project') || message.includes('portfolio') || message.includes('work')) {
      return botResponses.examples;
    } else if (message.includes('hello') || message.includes('hi') || message.includes('hey')) {
      return "Hello! 👋 Great to meet you! I'm NovaBot, and I'm here to help you discover how NovaWebb can transform your business with AI and cutting-edge web solutions. What brings you here today?";
    } else if (message.includes('contact') || message.includes('talk') || message.includes('meeting')) {
      return "I'd love to connect you with our team! 📞\n\nYou can:\n• Schedule a free consultation\n• Email <NAME_EMAIL>\n• Call us at +91-XXXX-XXXX\n\nOr just tell me about your project and I'll have someone reach out to you!";
    } else {
      return botResponses.default;
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate bot typing delay
    setTimeout(() => {
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: generateBotResponse(inputValue),
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);
  };

  const handleQuickResponse = (response: string) => {
    setInputValue(response);
    setTimeout(() => {
      handleSendMessage();
    }, 100);
  };

  return (
    <>
      {/* Chat Toggle Button */}
      <motion.button
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 2, type: "spring", stiffness: 200 }}
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 left-6 w-16 h-16 bg-primary rounded-full shadow-glow-primary z-40 flex items-center justify-center cursor-pointer hover:scale-110 transition-transform"
      >
        <MessageCircle className="h-8 w-8 text-primary-foreground" />
        <motion.div
          animate={{ 
            scale: [1, 1.2, 1],
            opacity: [1, 0.7, 1] 
          }}
          transition={{ 
            duration: 2, 
            repeat: Infinity,
            repeatType: "loop" 
          }}
          className="absolute inset-0 bg-primary rounded-full"
        />
      </motion.button>

      {/* Chat Window */}
      <div id="ai-chat" />
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0, y: 100 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0, y: 100 }}
            transition={{ type: "spring", stiffness: 200, damping: 20 }}
            className="fixed bottom-24 left-6 w-96 h-[500px] glass-ultra rounded-3xl z-40 flex flex-col overflow-hidden border border-primary/20"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
                    <Bot className="h-6 w-6 text-white" />
                  </div>
                  <motion.div
                    animate={{ scale: [1, 1.3, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full"
                  />
                </div>
                <div>
                  <h3 className="font-semibold">NovaBot</h3>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <Sparkles className="h-3 w-3" />
                    AI Assistant
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsOpen(false)}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20, scale: 0.8 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ type: "spring", stiffness: 200 }}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex items-start gap-2 max-w-[80%] ${
                    message.type === 'user' ? 'flex-row-reverse' : ''
                  }`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                      message.type === 'user' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-gradient-to-br from-secondary to-accent text-white'
                    }`}>
                      {message.type === 'user' ? (
                        <User className="h-4 w-4" />
                      ) : (
                        <Bot className="h-4 w-4" />
                      )}
                    </div>
                    <div className={`px-4 py-2 rounded-2xl ${
                      message.type === 'user'
                        ? 'bg-primary text-primary-foreground ml-2'
                        : 'bg-muted/50 mr-2'
                    }`}>
                      <p className="text-sm whitespace-pre-line leading-relaxed">
                        {message.content}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
              
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex justify-start"
                >
                  <div className="flex items-start gap-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-secondary to-accent rounded-full flex items-center justify-center">
                      <Bot className="h-4 w-4 text-white" />
                    </div>
                    <div className="bg-muted/50 px-4 py-2 rounded-2xl">
                      <div className="flex gap-1">
                        <motion.div
                          animate={{ scale: [1, 1.5, 1] }}
                          transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
                          className="w-2 h-2 bg-primary rounded-full"
                        />
                        <motion.div
                          animate={{ scale: [1, 1.5, 1] }}
                          transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
                          className="w-2 h-2 bg-primary rounded-full"
                        />
                        <motion.div
                          animate={{ scale: [1, 1.5, 1] }}
                          transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
                          className="w-2 h-2 bg-primary rounded-full"
                        />
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Quick Responses */}
            {messages.length <= 1 && (
              <div className="px-4 pb-2">
                <p className="text-xs text-muted-foreground mb-2">Quick questions:</p>
                <div className="flex flex-wrap gap-2">
                  {quickResponses.map((response) => (
                    <button
                      key={response}
                      onClick={() => handleQuickResponse(response)}
                      className="text-xs px-3 py-1 bg-muted/30 hover:bg-muted/50 rounded-full transition-colors"
                    >
                      {response}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Input */}
            <div className="p-4 border-t border-white/10">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Type your message..."
                  className="flex-1 px-4 py-2 bg-muted/30 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 border border-white/10"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim()}
                  size="icon"
                  className="w-10 h-10 rounded-full"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default AIChat;