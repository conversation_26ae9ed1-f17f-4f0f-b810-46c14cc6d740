import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import Navbar from '@/components/Navbar';
import Hero from '@/components/Hero';
import Services from '@/components/Services';
import StartupShowcase from '@/components/StartupShowcase';
import CustomCursor from '@/components/CustomCursor';
import NeuralNetwork from '@/components/NeuralNetwork';
import AnimatedMetrics from '@/components/AnimatedMetrics';
import ProcessTimeline from '@/components/ProcessTimeline';
import PortfolioShowcase from '@/components/PortfolioShowcase';
import TestimonialsCarousel from '@/components/TestimonialsCarousel';
import Contact from '@/components/Contact';
import AIChat from '@/components/AIChat';
import FloatingActionButton from '@/components/FloatingActionButton';
import LoadingScreen from '@/components/LoadingScreen';
import { useUltraSmoothScroll, ultraScrollUtils } from '@/hooks/useUltraSmoothScroll';

const Index = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isVisible, setIsVisible] = useState(false);

  // Initialize ultra-smooth scroll optimization
  const { 
    scrollState, 
    getPerformanceData 
  } = useUltraSmoothScroll({
    throttleMs: 8, // 120fps for ultra-smooth scrolling
    debounceMs: 50,
    enableParallax: true,
    enableSmoothScroll: true,
    enablePerformanceMonitoring: true
  });

  const handleLoadingComplete = () => {
    setIsLoading(false);
    // Small delay to ensure smooth transition
    setTimeout(() => setIsVisible(true), 100);
  };

  // Setup intersection observer for scroll animations
  useEffect(() => {
    if (!isVisible) return;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('in-view');
          // Enable hardware acceleration for animated elements
          if (entry.target instanceof HTMLElement) {
            ultraScrollUtils.enableHardwareAcceleration(entry.target);
          }
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    // Observe all elements with animate-on-scroll class
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    animatedElements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, [isVisible]);

  // Enable hardware acceleration for scroll container
  useEffect(() => {
    const scrollContainer = document.querySelector('#main-content');
    if (scrollContainer instanceof HTMLElement) {
      ultraScrollUtils.enableHardwareAcceleration(scrollContainer);
    }
  }, []);

  return (
    <>
      <AnimatePresence>
        {isLoading && (
          <LoadingScreen onComplete={handleLoadingComplete} />
        )}
      </AnimatePresence>
      
      <motion.div 
        id="main-content"
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoading ? 0 : 1 }}
        transition={{ duration: 0.5 }}
        className={`
          min-h-screen bg-gradient-hero relative overflow-hidden
          scroll-container hardware-accelerated
          ${isVisible ? 'fade-in' : ''}
        `}
        style={{
          // Performance optimizations
          willChange: 'scroll-position',
          transform: 'translateZ(0)',
          backfaceVisibility: 'hidden',
          perspective: '1000px'
        }}
      >
        {/* Performance-optimized cursor */}
        <CustomCursor />
        
        {/* Optimized neural network background */}
        <div className="fixed inset-0 z-0">
          <NeuralNetwork />
        </div>
        
        {/* Performance-optimized navbar */}
        <div className={`sticky top-0 z-50 transition-all duration-300 ${
          scrollState.y > 50 ? 'navbar-glass' : 'navbar-transparent'
        }`}>
          <Navbar />
        </div>
        
        {/* Ultra-smooth Hero Section */}
        <section 
          className="scroll-snap-section animate-on-scroll"
          data-parallax="0.2"
          style={{
            willChange: 'transform',
            transform: 'translateZ(0)'
          }}
        >
          <Hero />
        </section>

        {/* Animated Metrics with ultra-smooth performance */}
        <section 
          className="scroll-snap-section animate-on-scroll"
          data-parallax="0.15"
        >
          <AnimatedMetrics />
        </section>

        {/* Services with ultra-smooth animations */}
        <section 
          className="scroll-snap-section animate-on-scroll"
          data-parallax="0.1"
        >
          <Services />
        </section>

        {/* Startup Showcase with optimized performance */}
        <section 
          className="scroll-snap-section animate-on-scroll"
          data-parallax="0.12"
        >
          <StartupShowcase />
        </section>

        {/* Process Timeline with ultra-smooth scrolling */}
        <section 
          className="scroll-snap-section animate-on-scroll"
          data-parallax="0.08"
        >
          <ProcessTimeline />
        </section>

        {/* Portfolio Showcase with performance optimizations */}
        <section 
          className="scroll-snap-section animate-on-scroll"
          data-parallax="0.15"
        >
          <PortfolioShowcase />
        </section>

        {/* Testimonials with ultra-smooth animations */}
        <section 
          className="scroll-snap-section animate-on-scroll"
          data-parallax="0.1"
        >
          <TestimonialsCarousel />
        </section>

        {/* Contact section with optimized performance */}
        <section 
          className="scroll-snap-section animate-on-scroll"
          data-parallax="0.05"
        >
          <Contact />
        </section>
        
        {/* Performance-optimized AI Chat */}
        <AIChat />
        
        {/* Floating Action Button with ultra-smooth animations */}
        <FloatingActionButton />
      </motion.div>

      {/* Performance monitoring overlay (development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-4 right-4 z-[9999] bg-black/80 text-white p-2 rounded text-xs font-mono">
          <div>FPS: {getPerformanceData().fps}</div>
          <div>Scroll: {scrollState.direction || 'idle'}</div>
          <div>Y: {Math.round(scrollState.y)}</div>
          <div>Velocity: {scrollState.velocity.toFixed(2)}</div>
        </div>
      )}
    </>
  );
};

export default Index;