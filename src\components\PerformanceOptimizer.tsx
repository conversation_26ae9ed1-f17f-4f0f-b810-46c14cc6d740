import { useEffect, useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PerformanceOptimizerProps {
  children: React.ReactNode;
}

const PerformanceOptimizer = ({ children }: PerformanceOptimizerProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [performance, setPerformance] = useState({
    fps: 60,
    memory: 0,
    loadTime: 0
  });
  
  const rafIdRef = useRef<number>();
  const lastScrollTimeRef = useRef(0);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();

  // Advanced scroll performance optimization
  const optimizeScrollPerformance = useCallback(() => {
    // Throttle scroll events to 60fps
    const throttleScroll = (callback: () => void) => {
      const now = performance.now();
      if (now - lastScrollTimeRef.current >= 16.67) { // ~60fps
        callback();
        lastScrollTimeRef.current = now;
      }
    };

    // Optimize scroll performance
    const handleScroll = () => {
      throttleScroll(() => {
        // Use transform3d for hardware acceleration
        const scrolledElements = document.querySelectorAll('[data-scroll-optimized]');
        scrolledElements.forEach((element) => {
          if (element instanceof HTMLElement) {
            element.style.transform = `translate3d(0, ${window.scrollY * 0.5}px, 0)`;
          }
        });
      });
    };

    // Debounced scroll handler for heavy operations
    const handleScrollDebounced = () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      
      scrollTimeoutRef.current = setTimeout(() => {
        // Update scroll-based animations
        const scrollY = window.scrollY;
        const windowHeight = window.innerHeight;
        
        // Optimize parallax effects
        document.querySelectorAll('[data-parallax]').forEach((element) => {
          if (element instanceof HTMLElement) {
            const speed = parseFloat(element.dataset.parallax || '0.5');
            const yPos = -(scrollY * speed);
            element.style.transform = `translate3d(0, ${yPos}px, 0)`;
          }
        });
      }, 16); // ~60fps
    };

    // Add scroll listeners with passive option for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('scroll', handleScrollDebounced, { passive: true });

    // Optimize CSS for smooth scrolling
    document.documentElement.style.scrollBehavior = 'smooth';
    
    // Enable hardware acceleration for all animated elements
    const style = document.createElement('style');
    style.textContent = `
      .scroll-optimized {
        will-change: transform;
        transform: translateZ(0);
        backface-visibility: hidden;
        perspective: 1000px;
      }
      
      .hardware-accelerated {
        transform: translate3d(0, 0, 0);
        will-change: transform;
      }
      
      .smooth-scroll {
        scroll-behavior: smooth;
      }
      
      /* Optimize animations for performance */
      @media (prefers-reduced-motion: no-preference) {
        .animate-on-scroll {
          opacity: 0;
          transform: translateY(30px);
          transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .animate-on-scroll.in-view {
          opacity: 1;
          transform: translateY(0);
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('scroll', handleScrollDebounced);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      document.head.removeChild(style);
    };
  }, []);

  // Intersection Observer for scroll animations
  const setupIntersectionObserver = useCallback(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('in-view');
        }
      });
    }, observerOptions);

    // Observe all elements with animate-on-scroll class
    document.querySelectorAll('.animate-on-scroll').forEach((el) => {
      observer.observe(el);
    });

    return observer;
  }, []);

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);

    // Performance monitoring with fallback
    let frameCount = 0;
    let lastTime = 0;
    
    // Safe performance.now() with fallback
    const getCurrentTime = () => {
      if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
        return performance.now();
      }
      return Date.now();
    };
    
    lastTime = getCurrentTime();
    
    const measurePerformance = () => {
      frameCount++;
      const currentTime = getCurrentTime();
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        setPerformance(prev => ({
          ...prev,
          fps: Math.min(fps, 60)
        }));
        frameCount = 0;
        lastTime = currentTime;
      }
      
      rafIdRef.current = requestAnimationFrame(measurePerformance);
    };

    rafIdRef.current = requestAnimationFrame(measurePerformance);

    // Memory usage (if available)
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      const memoryInfo = (performance as any).memory;
      setPerformance(prev => ({
        ...prev,
        memory: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024) // MB
      }));
    }

    // Setup scroll optimizations
    const cleanupScroll = optimizeScrollPerformance();
    
    // Setup intersection observer after a short delay
    const observerTimer = setTimeout(() => {
      const observer = setupIntersectionObserver();
      
      return () => {
        observer.disconnect();
      };
    }, 500);

    return () => {
      clearTimeout(timer);
      clearTimeout(observerTimer);
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
      }
      cleanupScroll();
    };
  }, [optimizeScrollPerformance, setupIntersectionObserver]);

  return (
    <AnimatePresence mode="wait">
      {isLoaded ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="hardware-accelerated"
        >
          {children}
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 flex items-center justify-center bg-background z-50"
        >
          <div className="text-center space-y-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full mx-auto"
            />
            <p className="text-muted-foreground">Loading NovaWebb...</p>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PerformanceOptimizer;
