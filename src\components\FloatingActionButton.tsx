import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { 
  Plus, 
  MessageCircle, 
  Calendar, 
  Play, 
  Target, 
  X,
  Phone,
  Mail,
  Globe
} from 'lucide-react';

const FloatingActionButton = () => {
  const [isOpen, setIsOpen] = useState(false);

  const actions = [
    {
      icon: MessageCircle,
      label: 'Start Chat',
      description: 'Talk to our AI assistant',
      color: 'bg-blue-500',
      action: () => {
        // Trigger AI chat
        const chatElement = document.getElementById('ai-chat');
        if (chatElement) {
          chatElement.scrollIntoView({ behavior: 'smooth' });
        }
      }
    },
    {
      icon: Calendar,
      label: 'Schedule Call',
      description: 'Book a consultation',
      color: 'bg-emerald-500',
      action: () => {
        // Open calendar booking
        window.open('mailto:<EMAIL>?subject=Schedule%20Consultation', '_blank');
      }
    },
    {
      icon: Play,
      label: 'Start Project',
      description: 'Begin your journey',
      color: 'bg-purple-500',
      action: () => {
        // Scroll to contact form
        const contactElement = document.getElementById('contact');
        if (contactElement) {
          contactElement.scrollIntoView({ behavior: 'smooth' });
        }
      }
    },
    {
      icon: Target,
      label: 'View Portfolio',
      description: 'See our work',
      color: 'bg-orange-500',
      action: () => {
        // Scroll to portfolio
        const portfolioElement = document.getElementById('portfolio');
        if (portfolioElement) {
          portfolioElement.scrollIntoView({ behavior: 'smooth' });
        }
      }
    }
  ];

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute bottom-16 right-0 space-y-3"
          >
            {actions.map((action, index) => (
              <motion.button
                key={action.label}
                initial={{ opacity: 0, x: 20, scale: 0.8 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: 20, scale: 0.8 }}
                transition={{ delay: index * 0.1 }}
                onClick={action.action}
                className={`group flex items-center gap-3 ${action.color} text-white px-4 py-3 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105`}
              >
                <div className="relative">
                  <action.icon className="h-5 w-5" />
                  <div className="absolute inset-0 bg-white/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                <div className="text-left">
                  <div className="font-medium text-sm">{action.label}</div>
                  <div className="text-xs opacity-80">{action.description}</div>
                </div>
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main FAB */}
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => setIsOpen(!isOpen)}
        className={`relative w-14 h-14 rounded-full shadow-lg transition-all duration-300 ${
          isOpen 
            ? 'bg-red-500 hover:bg-red-600' 
            : 'bg-primary hover:bg-primary/90'
        }`}
      >
        <motion.div
          animate={{ rotate: isOpen ? 45 : 0 }}
          transition={{ duration: 0.3 }}
          className="flex items-center justify-center w-full h-full"
        >
          {isOpen ? (
            <X className="h-6 w-6 text-white" />
          ) : (
            <Plus className="h-6 w-6 text-white" />
          )}
        </motion.div>
        
        {/* Pulse effect */}
        <motion.div
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="absolute inset-0 rounded-full bg-primary/30"
        />
      </motion.button>

      {/* Quick contact info */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="absolute bottom-16 left-0 glass-card p-4 rounded-2xl min-w-[200px]"
          >
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-sm">
                <Phone className="h-4 w-4 text-primary" />
                <span>+****************</span>
              </div>
              <div className="flex items-center gap-3 text-sm">
                <Mail className="h-4 w-4 text-primary" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-3 text-sm">
                <Globe className="h-4 w-4 text-primary" />
                <span>novawebb.com</span>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default FloatingActionButton;
