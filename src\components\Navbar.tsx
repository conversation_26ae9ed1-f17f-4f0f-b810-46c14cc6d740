import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Menu, X, Brain, Sparkles, ChevronDown, ExternalLink } from 'lucide-react';
import { useNavbar } from '@/hooks/use-navbar';

const Navbar = () => {
  const [isHoveringLogo, setIsHoveringLogo] = useState(false);
  const [isHoveringCTA, setIsHoveringCTA] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const navbarRef = useRef<HTMLElement>(null);
  const { scrollY } = useScroll();
  
  // Use custom hook for navbar state management
  const {
    isScrolled,
    isMobileMenuOpen,
    activeSection,
    setIsMobileMenuOpen,
    handleSmoothScroll,
    handleLogoClick,
  } = useNavbar();

  // Set loaded state after component mounts
  useEffect(() => {
    setIsLoaded(true);
  }, []);
  
  // Advanced scroll-based animations
  const navbarOpacity = useTransform(scrollY, [0, 100], [0.8, 1]);
  const navbarBlur = useTransform(scrollY, [0, 100], [0, 20]);
  const logoScale = useTransform(scrollY, [0, 100], [1, 0.95]);

  const navLinks = [
    { 
      href: '#services', 
      label: 'Services',
      description: 'Our cutting-edge solutions',
      icon: Sparkles
    },
    { 
      href: '#showcase', 
      label: 'Showcase',
      description: 'Portfolio & achievements',
      icon: ExternalLink
    },
    { 
      href: '#about', 
      label: 'About',
      description: 'Our story & mission',
      icon: Brain
    },
    { 
      href: '#contact', 
      label: 'Contact',
      description: 'Get in touch',
      icon: ExternalLink
    },
  ];

  return (
    <>
      {/* Skip Link for Accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-[60] bg-primary text-primary-foreground px-4 py-2 rounded-lg font-medium hover:bg-primary/90 transition-colors"
      >
        Skip to main content
      </a>

      {/* Advanced Background Blur Effect */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-24 pointer-events-none z-40"
        style={{
          background: `linear-gradient(180deg, 
            hsl(var(--background) / ${isScrolled ? 0.95 : 0.3}) 0%, 
            hsl(var(--background) / ${isScrolled ? 0.8 : 0.1}) 50%, 
            transparent 100%)`,
          backdropFilter: `blur(${navbarBlur.get()}px)`,
        }}
      />

             <motion.nav
         ref={navbarRef}
         initial={{ y: -100, opacity: 0 }}
         animate={{ 
           y: isLoaded ? 0 : -100, 
           opacity: isLoaded ? 1 : 0 
         }}
         transition={{ 
           duration: 0.8, 
           ease: [0.25, 0.46, 0.45, 0.94],
           delay: 0.2
         }}
         style={{ opacity: navbarOpacity }}
         className={`fixed top-0 w-full z-50 transition-all duration-700 ${
           isScrolled
             ? 'glass-ultra border-b border-white/10 shadow-2xl'
             : 'bg-transparent'
         }`}
         role="navigation"
         aria-label="Main navigation"
       >
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Enhanced Logo with Advanced Animations */}
                         <motion.button
               whileHover={{ scale: 1.05 }}
               whileTap={{ scale: 0.95 }}
               onHoverStart={() => setIsHoveringLogo(true)}
               onHoverEnd={() => setIsHoveringLogo(false)}
               onClick={handleLogoClick}
               onKeyDown={(e) => {
                 if (e.key === 'Enter' || e.key === ' ') {
                   e.preventDefault();
                   handleLogoClick();
                 }
               }}
               className="flex items-center space-x-3 cursor-pointer group relative focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background rounded-lg p-1"
               style={{ scale: logoScale }}
               aria-label="Navigate to top of page"
             >
              {/* Animated Brain Icon */}
              <div className="relative">
                <motion.div
                  animate={isHoveringLogo ? { rotate: 360 } : { rotate: 0 }}
                  transition={{ duration: 0.6, ease: "easeInOut" }}
                >
                  <Brain className="h-8 w-8 text-primary" />
                </motion.div>
                
                {/* Pulsing Glow Effect */}
                <motion.div
                  animate={{ 
                    scale: [1, 1.2, 1],
                    opacity: [0.3, 0.6, 0.3]
                  }}
                  transition={{ 
                    duration: 2, 
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="absolute inset-0 bg-primary/30 rounded-full blur-xl"
                />
                
                {/* Sparkle Effect */}
                <AnimatePresence>
                  {isHoveringLogo && (
                    <motion.div
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0, opacity: 0 }}
                      className="absolute -top-1 -right-1"
                    >
                      <Sparkles className="h-4 w-4 text-accent animate-pulse" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Enhanced Brand Text */}
              <div className="relative">
                <motion.span 
                  className="text-2xl font-bold gradient-text"
                  animate={isHoveringLogo ? { 
                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                  } : {}}
                  transition={{ duration: 1.5, ease: "easeInOut" }}
                >
                  NovaWebb
                </motion.span>
                
                {/* Underline Animation */}
                <motion.div
                  className="absolute -bottom-1 left-0 h-0.5 bg-gradient-to-r from-primary via-secondary to-accent"
                  initial={{ width: 0 }}
                  animate={{ width: isHoveringLogo ? '100%' : 0 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                                 />
               </div>
             </motion.button>

            {/* Enhanced Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-1">
              {navLinks.map((link, index) => {
                const isActive = activeSection === link.href.substring(1);
                const Icon = link.icon;
                
                return (
                  <motion.div
                    key={link.href}
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ 
                      duration: 0.5, 
                      delay: 0.3 + index * 0.1,
                      ease: [0.25, 0.46, 0.45, 0.94]
                    }}
                    className="relative group"
                  >
                                         <motion.button
                       onClick={() => handleSmoothScroll(link.href)}
                       onKeyDown={(e) => {
                         if (e.key === 'Enter' || e.key === ' ') {
                           e.preventDefault();
                           handleSmoothScroll(link.href);
                         }
                       }}
                       whileHover={{ scale: 1.05 }}
                       whileTap={{ scale: 0.95 }}
                       className={`relative px-4 py-2 rounded-xl transition-all duration-300 flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background ${
                         isActive 
                           ? 'text-primary bg-primary/10 border border-primary/20' 
                           : 'text-foreground/80 hover:text-primary hover:bg-white/5'
                       }`}
                       aria-label={`Navigate to ${link.label} section`}
                       aria-current={isActive ? 'page' : undefined}
                     >
                      <Icon className="h-4 w-4" />
                      <span className="font-medium">{link.label}</span>
                      
                      {/* Active Indicator */}
                      {isActive && (
                        <motion.div
                          layoutId="activeIndicator"
                          className="absolute inset-0 bg-primary/10 border border-primary/20 rounded-xl"
                          initial={false}
                          transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        />
                      )}
                      
                      {/* Hover Glow */}
                      <motion.div
                        className="absolute inset-0 bg-primary/5 rounded-xl"
                        initial={{ opacity: 0 }}
                        whileHover={{ opacity: 1 }}
                        transition={{ duration: 0.2 }}
                      />
                    </motion.button>

                    {/* Tooltip */}
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      whileHover={{ opacity: 1, y: 0 }}
                      className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1 bg-card/90 backdrop-blur-xl border border-white/10 rounded-lg text-sm text-foreground/70 whitespace-nowrap pointer-events-none z-50"
                    >
                      {link.description}
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-card/90 border-l border-t border-white/10 rotate-45" />
                    </motion.div>
                  </motion.div>
                );
              })}
              
              {/* Enhanced CTA Button */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ 
                  duration: 0.5, 
                  delay: 0.7,
                  ease: [0.25, 0.46, 0.45, 0.94]
                }}
                className="ml-4"
              >
                <Button
                  variant="glow-primary"
                  size="sm"
                  onMouseEnter={() => setIsHoveringCTA(true)}
                  onMouseLeave={() => setIsHoveringCTA(false)}
                  className="relative overflow-hidden group"
                >
                  <motion.span
                    animate={isHoveringCTA ? { x: [0, 5, 0] } : { x: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    Get Started
                  </motion.span>
                  
                  {/* Shimmer Effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12"
                    initial={{ x: '-100%' }}
                    animate={isHoveringCTA ? { x: '100%' } : { x: '-100%' }}
                    transition={{ duration: 0.6, ease: "easeInOut" }}
                  />
                </Button>
              </motion.div>
            </div>

            {/* Enhanced Mobile Menu Button */}
            <div className="lg:hidden">
                             <motion.button
                 whileHover={{ scale: 1.05 }}
                 whileTap={{ scale: 0.95 }}
                 onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                 onKeyDown={(e) => {
                   if (e.key === 'Enter' || e.key === ' ') {
                     e.preventDefault();
                     setIsMobileMenuOpen(!isMobileMenuOpen);
                   }
                 }}
                 className="relative p-2 rounded-xl glass-card border border-white/10 hover:border-primary/30 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background"
                 aria-label={isMobileMenuOpen ? 'Close navigation menu' : 'Open navigation menu'}
                 aria-expanded={isMobileMenuOpen}
                 aria-controls="mobile-menu"
               >
                <AnimatePresence mode="wait">
                  {isMobileMenuOpen ? (
                    <motion.div
                      key="close"
                      initial={{ rotate: -90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: 90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <X className="h-6 w-6 text-foreground" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="menu"
                      initial={{ rotate: 90, opacity: 0 }}
                      animate={{ rotate: 0, opacity: 1 }}
                      exit={{ rotate: -90, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Menu className="h-6 w-6 text-foreground" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.button>
            </div>
          </div>

          {/* Enhanced Mobile Menu */}
          <AnimatePresence>
            {isMobileMenuOpen && (
                             <motion.div
                 id="mobile-menu"
                 initial={{ opacity: 0, height: 0, scale: 0.95 }}
                 animate={{ opacity: 1, height: 'auto', scale: 1 }}
                 exit={{ opacity: 0, height: 0, scale: 0.95 }}
                 transition={{ 
                   duration: 0.3,
                   ease: [0.25, 0.46, 0.45, 0.94]
                 }}
                 className="lg:hidden mt-4 glass-ultra rounded-2xl p-6 border border-white/10 overflow-hidden"
                 role="navigation"
                 aria-label="Mobile navigation menu"
               >
                <div className="flex flex-col space-y-2">
                  {navLinks.map((link, index) => {
                    const isActive = activeSection === link.href.substring(1);
                    const Icon = link.icon;
                    
                    return (
                                             <motion.button
                         key={link.href}
                         initial={{ opacity: 0, x: -20 }}
                         animate={{ opacity: 1, x: 0 }}
                         transition={{ 
                           duration: 0.3, 
                           delay: index * 0.1,
                           ease: [0.25, 0.46, 0.45, 0.94]
                         }}
                         onClick={() => handleSmoothScroll(link.href)}
                         onKeyDown={(e) => {
                           if (e.key === 'Enter' || e.key === ' ') {
                             e.preventDefault();
                             handleSmoothScroll(link.href);
                           }
                         }}
                         className={`flex items-center space-x-3 p-3 rounded-xl transition-all duration-300 text-left focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background ${
                           isActive 
                             ? 'text-primary bg-primary/10 border border-primary/20' 
                             : 'text-foreground/80 hover:text-primary hover:bg-white/5'
                         }`}
                         aria-label={`Navigate to ${link.label} section`}
                         aria-current={isActive ? 'page' : undefined}
                       >
                        <Icon className="h-5 w-5 flex-shrink-0" />
                        <div className="flex-1">
                          <div className="font-medium">{link.label}</div>
                          <div className="text-sm text-foreground/60">{link.description}</div>
                        </div>
                        <ChevronDown className="h-4 w-4 text-foreground/40" />
                      </motion.button>
                    );
                  })}
                  
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ 
                      duration: 0.3, 
                      delay: 0.4,
                      ease: [0.25, 0.46, 0.45, 0.94]
                    }}
                    className="pt-4"
                  >
                    <Button 
                      variant="glow-primary" 
                      className="w-full"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Get Started
                    </Button>
                  </motion.div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.nav>

      {/* Scroll Progress Indicator */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary via-secondary to-accent z-50 origin-left"
        style={{ scaleX: useTransform(scrollY, [0, document.body.scrollHeight - window.innerHeight], [0, 1]) }}
      />
    </>
  );
};

export default Navbar;