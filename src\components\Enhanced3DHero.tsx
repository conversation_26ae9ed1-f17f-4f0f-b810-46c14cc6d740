import { useRef, useMemo } from 'react';
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from '@react-three/fiber';
import { Float, Sphere, Torus, Box, MeshDistortMaterial, Environment, OrbitControls } from '@react-three/drei';
import * as THREE from 'three';

const FloatingGeometry = () => {
  const meshRef = useRef<THREE.Group>(null);
  const { viewport, mouse } = useThree();
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.4) * 0.2;
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.3;
      meshRef.current.rotation.z = Math.cos(state.clock.elapsedTime * 0.2) * 0.1;
      
      // Mouse interaction
      meshRef.current.rotation.y += mouse.x * 0.1;
      meshRef.current.rotation.x += mouse.y * 0.1;
    }
  });

  return (
    <Float speed={2} rotationIntensity={0.4} floatIntensity={0.6}>
      <group ref={meshRef} scale={viewport.width > 6 ? 2 : 1.2}>
        {/* Main AI Brain Core */}
        <Sphere args={[1, 64, 64]}>
          <MeshDistortMaterial
            color="#4F46E5"
            attach="material"
            distort={0.4}
            speed={2}
            roughness={0.1}
            metalness={0.8}
            emissive="#4F46E5"
            emissiveIntensity={0.2}
          />
        </Sphere>
        
        {/* Orbiting rings */}
        <Torus 
          args={[1.5, 0.1, 16, 100]}
          position={[0, 0, 0]}
          rotation={[Math.PI / 2, 0, 0]}
        >
          <meshStandardMaterial
            color="#00D4AA" 
            transparent
            opacity={0.7}
            emissive="#00D4AA"
            emissiveIntensity={0.3}
          />
        </Torus>
        
        <Torus 
          args={[2, 0.05, 16, 100]}
          position={[0, 0, 0]}
          rotation={[0, Math.PI / 4, Math.PI / 3]}
        >
          <meshStandardMaterial
            color="#A855F7" 
            transparent
            opacity={0.5}
            emissive="#A855F7"
            emissiveIntensity={0.2}
          />
        </Torus>
        
        {/* Floating data cubes */}
        {Array.from({ length: 8 }).map((_, i) => (
          <Float key={i} speed={3 + i} rotationIntensity={1} floatIntensity={2}>
            <Box
              args={[0.15, 0.15, 0.15]}
              position={[
                Math.cos((i / 8) * Math.PI * 2) * 3,
                Math.sin((i / 8) * Math.PI * 2) * 1.5,
                Math.sin((i / 8) * Math.PI * 4) * 2
              ]}
            >
              <meshStandardMaterial
                color={i % 3 === 0 ? "#4F46E5" : i % 3 === 1 ? "#00D4AA" : "#A855F7"}
                emissive={i % 3 === 0 ? "#4F46E5" : i % 3 === 1 ? "#00D4AA" : "#A855F7"}
                emissiveIntensity={0.5}
                transparent
                opacity={0.8}
              />
            </Box>
          </Float>
        ))}
      </group>
    </Float>
  );
};

const ParticleField = () => {
  const points = useRef<THREE.Points>(null);
  
  const particlesPosition = useMemo(() => {
    const positions = new Float32Array(2000 * 3);
    
    for (let i = 0; i < 2000; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 20;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 20;
    }
    
    return positions;
  }, []);

  useFrame((state) => {
    if (points.current) {
      points.current.rotation.y = state.clock.elapsedTime * 0.05;
      points.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.1;
    }
  });

  return (
    <points ref={points}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={2000}
          array={particlesPosition}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        color="#4F46E5"
        size={0.01}
        transparent
        opacity={0.4}
        sizeAttenuation
        vertexColors={false}
      />
    </points>
  );
};

const Enhanced3DHero = () => {
  return (
    <div className="w-full h-full min-h-[600px] relative">
      <Canvas
        camera={{ 
          position: [0, 0, 8], 
          fov: 45,
          near: 0.1,
          far: 100
        }}
        style={{ background: 'transparent' }}
        gl={{ 
          antialias: true, 
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        <ambientLight intensity={0.3} />
        <directionalLight position={[5, 5, 5]} intensity={1} castShadow />
        <pointLight position={[-5, -5, -5]} color="#00D4AA" intensity={0.5} />
        <pointLight position={[5, -5, 5]} color="#A855F7" intensity={0.3} />
        
        <Environment preset="city" />
        
        <ParticleField />
        <FloatingGeometry />
        
        <OrbitControls 
          enableZoom={false}
          enablePan={false}
          autoRotate
          autoRotateSpeed={0.5}
        />
      </Canvas>
      
      {/* Gradient overlays for integration */}
      <div className="absolute inset-0 bg-gradient-to-t from-background/40 via-transparent to-background/20 pointer-events-none" />
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-background to-transparent pointer-events-none" />
    </div>
  );
};

export default Enhanced3DHero;