import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { ExternalLink, Github, ArrowRight, Zap, Globe, Brain } from 'lucide-react';
import { Button } from '@/components/ui/button';

const PortfolioShowcase = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const [selectedProject, setSelectedProject] = useState(0);

  const projects = [
    {
      id: 1,
      title: 'AI-Powered E-Commerce Platform',
      category: 'AI Solutions',
      description: 'Revolutionary e-commerce platform with AI-driven product recommendations and intelligent inventory management.',
      longDescription: 'Built a comprehensive e-commerce solution featuring machine learning algorithms for personalized recommendations, automated inventory optimization, and intelligent customer support chatbots. The platform increased conversion rates by 45% and reduced operational costs by 30%.',
      image: '/placeholder-project-1.jpg',
      tags: ['React', 'Node.js', 'TensorFlow', 'MongoDB'],
      liveUrl: '#',
      githubUrl: '#',
      metrics: {
        performance: '45% increase in conversions',
        efficiency: '30% cost reduction',
        satisfaction: '98% customer satisfaction'
      },
      color: 'primary'
    },
    {
      id: 2,
      title: 'Smart Analytics Dashboard',
      category: 'Web Development',
      description: 'Real-time analytics dashboard with advanced data visualization and predictive insights for business intelligence.',
      longDescription: 'Developed a sophisticated analytics platform that processes millions of data points in real-time, featuring interactive visualizations, custom reporting, and AI-powered predictive analytics. The dashboard enables data-driven decision making across all business operations.',
      image: '/placeholder-project-2.jpg',
      tags: ['Next.js', 'D3.js', 'Python', 'PostgreSQL'],
      liveUrl: '#',
      githubUrl: '#',
      metrics: {
        performance: '99.9% uptime',
        efficiency: '5x faster insights',
        satisfaction: '95% user adoption'
      },
      color: 'secondary'
    },
    {
      id: 3,
      title: 'Neural Network Trading Bot',
      category: 'AI Solutions',
      description: 'Advanced trading bot using deep learning algorithms for cryptocurrency market analysis and automated trading.',
      longDescription: 'Created an intelligent trading system that leverages deep neural networks to analyze market patterns, sentiment analysis, and technical indicators. The bot achieved consistent returns while managing risk through sophisticated algorithms and real-time market monitoring.',
      image: '/placeholder-project-3.jpg',
      tags: ['Python', 'TensorFlow', 'FastAPI', 'Redis'],
      liveUrl: '#',
      githubUrl: '#',
      metrics: {
        performance: '23% average returns',
        efficiency: '0.1% slippage',
        satisfaction: '87% win rate'
      },
      color: 'accent'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  return (
    <section id="portfolio" className="py-24 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 neural-grid opacity-15" />
      <div className="absolute top-1/4 right-0 w-[600px] h-[600px] bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 left-0 w-[400px] h-[400px] bg-secondary/5 rounded-full blur-3xl" />
      
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 glass-card px-6 py-3 rounded-full mb-6"
          >
            <Zap className="h-5 w-5 text-primary animate-pulse" />
            <span className="font-medium">Our Work</span>
          </motion.div>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            Portfolio{' '}
            <span className="gradient-text">Showcase</span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Explore our latest projects that demonstrate the power of AI-driven solutions 
            and cutting-edge web technologies.
          </p>
        </motion.div>

        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid lg:grid-cols-2 gap-12 items-center"
        >
          {/* Project Selector */}
          <div className="space-y-6">
            <h3 className="text-2xl font-bold mb-8">Featured Projects</h3>
            
            {projects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, x: -30 }}
                animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: -30 }}
                transition={{ delay: index * 0.2 }}
                onClick={() => setSelectedProject(index)}
                className={`glass-card p-6 cursor-pointer transition-all duration-300 border-2 ${
                  selectedProject === index 
                    ? `border-${project.color} shadow-glow-${project.color}` 
                    : 'border-transparent hover:border-white/20'
                }`}
              >
                <div className="flex items-start gap-4">
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    className={`p-3 rounded-xl ${
                      project.color === 'primary' ? 'bg-primary/10 text-primary' :
                      project.color === 'secondary' ? 'bg-secondary/10 text-secondary' :
                      'bg-accent/10 text-accent'
                    }`}
                  >
                    {project.category === 'AI Solutions' ? <Brain className="h-6 w-6" /> : <Globe className="h-6 w-6" />}
                  </motion.div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-mono text-muted-foreground uppercase tracking-wide">
                        {project.category}
                      </span>
                      {selectedProject === index && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="w-2 h-2 bg-primary rounded-full"
                        />
                      )}
                    </div>
                    
                    <h4 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                      {project.title}
                    </h4>
                    
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {project.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Project Details */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: 30 }}
            transition={{ delay: 0.4 }}
            className="relative"
          >
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedProject}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="glass-ultra p-8 space-y-6"
              >
                {/* Project Image */}
                <div className="relative h-64 bg-gradient-to-br from-muted/20 to-muted/5 rounded-2xl overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className={`inline-flex p-6 rounded-3xl mb-4 ${
                        projects[selectedProject].color === 'primary' ? 'bg-primary/10' :
                        projects[selectedProject].color === 'secondary' ? 'bg-secondary/10' :
                        'bg-accent/10'
                      }`}>
                        <Globe className={`h-12 w-12 ${
                          projects[selectedProject].color === 'primary' ? 'text-primary' :
                          projects[selectedProject].color === 'secondary' ? 'text-secondary' :
                          'text-accent'
                        }`} />
                      </div>
                      <p className="text-muted-foreground">Project Preview</p>
                    </div>
                  </div>
                </div>

                {/* Project Info */}
                <div>
                  <h3 className="text-2xl font-bold mb-4">
                    {projects[selectedProject].title}
                  </h3>
                  
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {projects[selectedProject].longDescription}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-6">
                    {projects[selectedProject].tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-3 py-1 text-xs font-medium bg-muted/50 rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Metrics */}
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    {Object.entries(projects[selectedProject].metrics).map(([key, value]) => (
                      <div key={key} className="text-center">
                        <div className={`text-lg font-bold ${
                          projects[selectedProject].color === 'primary' ? 'text-primary' :
                          projects[selectedProject].color === 'secondary' ? 'text-secondary' :
                          'text-accent'
                        }`}>
                          {value}
                        </div>
                        <div className="text-xs text-muted-foreground capitalize">
                          {key}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Actions */}
                  <div className="flex gap-4">
                    <Button variant="glow-primary" size="sm" className="flex-1">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View Live
                    </Button>
                    <Button variant="glass" size="sm" className="flex-1">
                      <Github className="mr-2 h-4 w-4" />
                      Source Code
                    </Button>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </motion.div>
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="text-center mt-20"
        >
          <p className="text-muted-foreground mb-6 text-lg">
            Interested in seeing more of our work or starting your own project?
          </p>
          <Button variant="gradient" size="lg" className="group">
            View All Projects
            <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default PortfolioShowcase;