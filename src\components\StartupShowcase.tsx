import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { useRef, useState } from 'react';
import { 
  Rocket, 
  Zap, 
  TrendingUp, 
  Users, 
  Target, 
  ArrowRight,
  Play,
  Code,
  Brain,
  Globe,
  Smartphone,
  Cloud,
  Shield,
  CheckCircle,
  Star,
  Award
} from 'lucide-react';

const StartupShowcase = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState(0);
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });
  
  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);
  const scale = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.8, 1, 1, 0.8]);
  
  const springY = useSpring(y, { stiffness: 100, damping: 30 });
  const springOpacity = useSpring(opacity, { stiffness: 100, damping: 30 });
  const springScale = useSpring(scale, { stiffness: 100, damping: 30 });

  const showcaseData = [
    {
      id: 0,
      title: "AI-Powered Web Solutions",
      subtitle: "Intelligent Automation",
      description: "We build cutting-edge AI agents and web applications that transform how businesses operate in the digital age.",
      features: [
        "Custom AI chatbots and virtual assistants",
        "Intelligent process automation systems",
        "Predictive analytics and data insights",
        "Natural language processing solutions"
      ],
      stats: { efficiency: "300%", accuracy: "99.9%", speed: "10x faster" },
      icon: Brain,
      color: "blue",
      gradient: "from-blue-500/20 via-purple-500/20 to-cyan-500/20"
    },
    {
      id: 1,
      title: "Modern Web Development",
      subtitle: "Cutting-Edge Technology",
      description: "Create stunning, high-performance web applications with the latest technologies and best practices.",
      features: [
        "Responsive and progressive web apps",
        "React, Next.js, and modern frameworks",
        "Performance optimization and SEO",
        "E-commerce and business solutions"
      ],
      stats: { performance: "100%", satisfaction: "5.0/5", uptime: "99.9%" },
      icon: Code,
      color: "emerald",
      gradient: "from-emerald-500/20 via-teal-500/20 to-green-500/20"
    },
    {
      id: 2,
      title: "Custom Integrations",
      subtitle: "Seamless Connections",
      description: "Connect your systems with powerful APIs and integrations that work perfectly with your existing infrastructure.",
      features: [
        "RESTful and GraphQL API development",
        "Third-party service integrations",
        "Database design and optimization",
        "Security and compliance solutions"
      ],
      stats: { reliability: "99.9%", security: "Enterprise-grade", support: "24/7" },
      icon: Cloud,
      color: "orange",
      gradient: "from-orange-500/20 via-red-500/20 to-pink-500/20"
    }
  ];

  const achievements = [
    { icon: Award, title: "Innovation Award", description: "Recognized for cutting-edge AI solutions" },
    { icon: Users, title: "50+ Clients", description: "Trusted by businesses worldwide" },
    { icon: TrendingUp, title: "300% Growth", description: "Consistent year-over-year growth" },
    { icon: Star, title: "5.0 Rating", description: "Perfect client satisfaction score" }
  ];

  return (
    <section ref={containerRef} className="py-32 relative overflow-hidden min-h-screen">
      {/* Advanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-background via-background/95 to-background" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/10 via-transparent to-transparent" />
      
      {/* Floating elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-10, 10, -10],
              x: [-5, 5, -5],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: Math.random() * 2
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            className="absolute w-1 h-1 bg-primary/40 rounded-full blur-sm"
          />
        ))}
      </div>

      <motion.div 
        style={{ y: springY, opacity: springOpacity, scale: springScale }}
        className="container mx-auto px-6 relative z-10"
      >
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-3 glass-card px-8 py-4 rounded-full mb-8 group cursor-pointer hover:bg-white/10 transition-all duration-300"
          >
            <Rocket className="h-5 w-5 text-primary animate-pulse" />
            <span className="font-semibold text-sm tracking-wide">Startup Innovation</span>
            <ArrowRight className="h-4 w-4 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
          </motion.div>
          
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-5xl md:text-7xl font-bold mb-8 leading-tight"
          >
            Building the Future of{' '}
            <span className="gradient-text bg-gradient-to-r from-primary via-purple-500 to-cyan-500 bg-clip-text text-transparent">
              Digital Innovation
            </span>
          </motion.h2>
          
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed mb-12"
          >
            We're a startup passionate about creating intelligent web solutions and AI agents that drive business growth. 
            Our cutting-edge technology stack and innovative approach set us apart in the digital landscape.
          </motion.p>

          {/* Achievement Cards */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
          >
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.title}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                className="glass-card p-6 text-center group hover:bg-white/10 transition-all duration-300"
              >
                <achievement.icon className="h-8 w-8 text-primary mx-auto mb-3 group-hover:scale-110 transition-transform" />
                <h3 className="font-bold text-lg mb-1">{achievement.title}</h3>
                <p className="text-sm text-muted-foreground">{achievement.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Interactive Showcase Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mb-16"
        >
          {/* Tab Navigation */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {showcaseData.map((item, index) => (
              <motion.button
                key={item.id}
                onClick={() => setActiveTab(index)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`flex items-center gap-3 px-6 py-4 rounded-2xl font-medium transition-all duration-300 ${
                  activeTab === index
                    ? 'glass-card bg-white/10 border border-white/20'
                    : 'glass-card hover:bg-white/5'
                }`}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.title}</span>
              </motion.button>
            ))}
          </div>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="glass-card p-12 rounded-3xl relative overflow-hidden"
          >
            <div className={`absolute inset-0 bg-gradient-to-br ${showcaseData[activeTab].gradient} opacity-10`} />
            
            <div className="relative grid lg:grid-cols-2 gap-12 items-center">
              {/* Content */}
              <div>
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="text-sm font-medium text-primary mb-3"
                >
                  {showcaseData[activeTab].subtitle}
                </motion.div>
                
                <motion.h3
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="text-3xl md:text-4xl font-bold mb-6"
                >
                  {showcaseData[activeTab].title}
                </motion.h3>
                
                <motion.p
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="text-muted-foreground mb-8 leading-relaxed"
                >
                  {showcaseData[activeTab].description}
                </motion.p>

                {/* Features */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  className="space-y-4 mb-8"
                >
                  {showcaseData[activeTab].features.map((feature, index) => (
                    <motion.div
                      key={feature}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                      className="flex items-center gap-3"
                    >
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                      <span>{feature}</span>
                    </motion.div>
                  ))}
                </motion.div>

                {/* Stats */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.7 }}
                  className="grid grid-cols-3 gap-4"
                >
                  {Object.entries(showcaseData[activeTab].stats).map(([key, value]) => (
                    <div key={key} className="text-center">
                      <div className="text-2xl font-bold text-primary">{value}</div>
                      <div className="text-xs text-muted-foreground capitalize">{key}</div>
                    </div>
                  ))}
                </motion.div>
              </div>

              {/* Visual/Icon */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex justify-center lg:justify-end"
              >
                                 <div className={`relative p-12 rounded-3xl bg-gradient-to-br ${showcaseData[activeTab].gradient}`}>
                   {(() => {
                     const IconComponent = showcaseData[activeTab].icon;
                     return <IconComponent className="h-24 w-24 text-white" />;
                   })()}
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/20 to-transparent" />
                </div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.7 }}
          className="text-center"
        >
          <div className="glass-card p-12 rounded-3xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-purple-500/10 to-cyan-500/10" />
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent" />
            
            <div className="relative">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-primary/20 text-primary mb-6"
              >
                <Zap className="h-5 w-5" />
                <span className="font-medium">Ready to Innovate?</span>
              </motion.div>
              
              <h3 className="text-3xl md:text-4xl font-bold mb-4">
                Let's Build Something{' '}
                <span className="gradient-text bg-gradient-to-r from-primary to-purple-500 bg-clip-text text-transparent">
                  Extraordinary
                </span>
              </h3>
              
              <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
                Join us in shaping the future of digital innovation. Whether you need AI agents, 
                modern web applications, or custom integrations, we're here to bring your vision to life.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center gap-3 px-8 py-4 bg-primary text-white rounded-2xl font-medium hover:bg-primary/90 transition-colors group"
                >
                  <Play className="h-5 w-5 group-hover:scale-110 transition-transform" />
                  Start Your Project
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center gap-3 px-8 py-4 glass-card rounded-2xl font-medium hover:bg-white/10 transition-colors group"
                >
                  <Target className="h-5 w-5 group-hover:scale-110 transition-transform" />
                  View Our Work
                </motion.button>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default StartupShowcase;
