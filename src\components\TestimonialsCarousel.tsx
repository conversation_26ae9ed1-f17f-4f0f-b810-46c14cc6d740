import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react';
import { Button } from '@/components/ui/button';

const TestimonialsCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      role: 'CEO, TechStart Inc.',
      company: 'TechStart Inc.',
      image: '/placeholder-avatar-1.jpg',
      rating: 5,
      text: "NovaWebb transformed our business with their AI-powered solutions. The intelligent chatbot they built increased our customer satisfaction by 40% and reduced response times dramatically.",
      project: 'AI Customer Support System',
      results: '40% increased satisfaction'
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'CTO, DataFlow Solutions',
      company: 'DataFlow Solutions',
      image: '/placeholder-avatar-2.jpg',
      rating: 5,
      text: "The team at NovaWebb delivered an exceptional analytics dashboard that revolutionized how we visualize data. Their attention to detail and technical expertise is unmatched.",
      project: 'Real-time Analytics Platform',
      results: '5x faster data insights'
    },
    {
      id: 3,
      name: 'Emily Rodriguez',
      role: 'Founder, EcoMarket',
      company: 'EcoMarket',
      image: '/placeholder-avatar-3.jpg',
      rating: 5,
      text: "Working with NovaWebb was a game-changer. They built us a complete e-commerce platform with AI recommendations that boosted our sales by 60%. Absolutely incredible!",
      project: 'AI E-commerce Platform',
      results: '60% sales increase'
    },
    {
      id: 4,
      name: 'David Park',
      role: 'Director, FinanceAI',
      company: 'FinanceAI',
      image: '/placeholder-avatar-4.jpg',
      rating: 5,
      text: "The trading bot NovaWebb developed for us has been consistently profitable. Their deep understanding of both AI and financial markets is impressive.",
      project: 'Neural Trading Algorithm',
      results: '23% average returns'
    }
  ];

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, testimonials.length]);

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  return (
    <section id="about" className="py-24 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-background via-muted/5 to-background" />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-accent/5 rounded-full blur-3xl" />
      
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 glass-card px-6 py-3 rounded-full mb-6"
          >
            <Star className="h-5 w-5 text-primary animate-pulse" />
            <span className="font-medium">Client Stories</span>
          </motion.div>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            What Our Clients{' '}
            <span className="gradient-text">Say</span>
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Don't just take our word for it. Hear from the businesses we've helped transform 
            with our AI solutions and web development expertise.
          </p>
        </motion.div>

        {/* Carousel Container */}
        <div 
          className="relative max-w-6xl mx-auto"
          onMouseEnter={() => setIsAutoPlaying(false)}
          onMouseLeave={() => setIsAutoPlaying(true)}
        >
          {/* Main Carousel */}
          <div className="relative h-[400px] lg:h-[350px] overflow-hidden rounded-3xl">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 300, rotateY: 90 }}
                animate={{ opacity: 1, x: 0, rotateY: 0 }}
                exit={{ opacity: 0, x: -300, rotateY: -90 }}
                transition={{ 
                  duration: 0.8, 
                  type: "spring", 
                  stiffness: 100,
                  damping: 20
                }}
                className="absolute inset-0"
              >
                <div className="glass-ultra p-8 lg:p-12 h-full flex flex-col lg:flex-row items-center gap-8">
                  {/* Avatar and Info */}
                  <div className="flex-shrink-0 text-center lg:text-left">
                    <motion.div
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                      className="relative w-24 h-24 mx-auto lg:mx-0 mb-4"
                    >
                      <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center">
                        <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="text-2xl font-bold text-primary">
                            {testimonials[currentIndex].name.charAt(0)}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                    
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      <h4 className="text-xl font-semibold mb-1">
                        {testimonials[currentIndex].name}
                      </h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        {testimonials[currentIndex].role}
                      </p>
                      <p className="text-xs text-primary font-medium">
                        {testimonials[currentIndex].company}
                      </p>
                    </motion.div>

                    {/* Rating */}
                    <motion.div
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.4 }}
                      className="flex justify-center lg:justify-start gap-1 mt-4"
                    >
                      {Array.from({ length: testimonials[currentIndex].rating }).map((_, i) => (
                        <Star
                          key={i}
                          className="w-4 h-4 fill-primary text-primary"
                        />
                      ))}
                    </motion.div>
                  </div>

                  {/* Testimonial Content */}
                  <div className="flex-1 lg:pl-8">
                    <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 }}
                      className="relative"
                    >
                      <Quote className="absolute -top-4 -left-4 h-8 w-8 text-primary/20" />
                      
                      <blockquote className="text-lg lg:text-xl leading-relaxed mb-6 pl-4">
                        "{testimonials[currentIndex].text}"
                      </blockquote>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">Project:</p>
                          <p className="font-medium">{testimonials[currentIndex].project}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Results:</p>
                          <p className="font-medium text-primary">{testimonials[currentIndex].results}</p>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation Arrows */}
          <Button
            variant="glass"
            size="icon"
            onClick={goToPrevious}
            className="absolute top-1/2 -translate-y-1/2 -left-4 lg:-left-16 w-12 h-12 rounded-full"
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>

          <Button
            variant="glass"
            size="icon"
            onClick={goToNext}
            className="absolute top-1/2 -translate-y-1/2 -right-4 lg:-right-16 w-12 h-12 rounded-full"
          >
            <ChevronRight className="h-6 w-6" />
          </Button>

          {/* Dots Indicator */}
          <div className="flex justify-center gap-3 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-primary shadow-glow-primary scale-125'
                    : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mt-20"
        >
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">5.0</div>
            <div className="text-sm text-muted-foreground">Average Rating</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-secondary mb-2">100%</div>
            <div className="text-sm text-muted-foreground">Project Success</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-accent mb-2">48h</div>
            <div className="text-sm text-muted-foreground">Response Time</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">150+</div>
            <div className="text-sm text-muted-foreground">Happy Clients</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TestimonialsCarousel;